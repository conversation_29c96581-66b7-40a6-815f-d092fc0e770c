const { Op } = require('sequelize');

/**
 * 
 * @param {*} model 
 * @param {Object[]} data
 * @param {Number} data[].accountId 
 * @param {String} data[].status
 * @param {String} data[].email
 * @param {String} data[].errorReason
 * @param {String} data[].environment
 * @param {String} data[].sub
 * @returns 
 */
async function insertData(model, data) {
    if (!Array.isArray(data)) {
        data = [data];
    }
    try {
        return model.bulkCreate(data,  {
            updateOnDuplicate: ["accountId"] 
        });
    } catch (error) {
        console.log(error);
    }
}

async function selectData(model, query = {}) {
    let data = await model.findAll({
        where: {
            ...query
        }
    }).then(response => {
        return JSON.parse(JSON.stringify(response))
    })
    return data;
}

async function updateData(model, data, ids) {
    try {
        return model.update(data, {
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });
    } catch (error) {
        console.log(error);
    }
}

module.exports = { insertData, selectData, updateData } 
