const email = require("../utils/email");
const fs = require("fs");

class MailService { 
  constructor() {}
  async _sendReportEmail(pathToAttachment, clientEmail) {
    if (!clientEmail) {
      return {
        emailSent: false,
        emailData: {
          errorReason: "client has no email",
          errorSource: "client has no email",
        },
      };
    }
    const fileName = "Proclamation Demand Notice.pdf";
    console.log({
      pathToAttachment,
    });
    const attachment = fs.readFileSync(pathToAttachment).toString("base64");

    try {
      let message =
        "Please find attached file for your Final Proclamtion Notice ";

      let data = {
        to: process.env.EMAIL_SETTING == "local" ? "<EMAIL>" : clientEmail,
      
        subject: ` ${fileName} `,
        emailBody: message,
        tenantId: "platinumKenya",
        attachments: [
          {
            content: attachment,
            filename: fileName,
            contentType: "application/pdf",
            disposition: "attachment",
          },
        ],
      };

      const resp = await email.sendEmail(data);
      return {
        emailSent: true,
        emailData: resp,
      };
    } catch (error) {
      console.log(JSON.stringify(error, null, 3));
      const failedObj = error?.response?.body?.errors[0] || {
        errorReason: error.message ?? "unknown",
        errorSource: "sendgrid",
      };
      return {
        emailSent: false,
        emailData: {
          errorReason: failedObj.message ?? "unknown",
          errorSource: failedObj.field ?? "sendgrid",
        },
      };
    }
  }
}

module.exports = new MailService();

