const { Sequelize, DataTypes } = require('sequelize');

module.exports = (app) => {
  const sequelize = app.get('sequelize');
  const Report = sequelize.define('report', {
    // Model attributes are defined here
    accountId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true
    },
    email_attached:{
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "fileReceived",
    },

    email: {
      type: DataTypes.STRING,
      allowNull: false
    },
    errorReason: {
      type: DataTypes.STRING,
      allowNull: true,
    },

    environment: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sub: {
      type: DataTypes.STRING,
      allowNull: true,
    },

    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },

    

  });

  return Report;
}



