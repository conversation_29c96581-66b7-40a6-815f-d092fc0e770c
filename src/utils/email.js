
const axios = require("axios");

const email = {
  loadApi: () => {
    axios.defaults.baseURL = process.env.EMAIL_API_URL;
    axios.defaults.headers.common["apiKey"] = process.env.EMAIL_API_KEY;
    return axios;
  },
  sendEmail: async ({
    to,
    subject,
    emailBody,
    tenant = "platinumKenya",
    attachments = [],
  }) => {
    const emailSent = await email.loadApi().post("/email", {
      to,
      subject,
      body: emailBody,
      tenantId: tenant,
      groupRecordId: "pkeUserAccess",
      attachments,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return emailSent;
  },
};

module.exports = email;
