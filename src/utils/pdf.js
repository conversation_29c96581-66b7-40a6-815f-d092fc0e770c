const pdfMake = require("pdfmake");
const htmlToPdfmake = require("html-to-pdfmake");
const fs = require("fs");
const path = require("path");
const { JSDOM } = require("jsdom");


/**
 * @param(string) htmlFile      the dynamic html file
 * @param(string) filename      the name for the pdf 
 * 
 * @returns(object)
 */

function generatePdfFromHtml(dd, file_name) {


  const _path = path.join(__dirname, `../views/docs/${file_name}`);


  let buffers = [];
  var fonts = {
    Roboto: {
      normal: path.join(__dirname, "Roboto", "/Roboto-Regular.ttf"),
      bold: path.join(__dirname, "Roboto", "/Roboto-Medium.ttf"),
      italics: path.join(__dirname, "Roboto", "/Roboto-Italic.ttf"),
      bolditalics: path.join(
        __dirname,
        "Roboto",
        "/fonts/Roboto-MediumItalic.ttf"
      ),
    },
    Times: {
      normal: path.join(__dirname, "Times", "/times new roman.ttf"),
      bold: path.join(__dirname, "Times", "/times new roman bold.ttf"),
      italics: path.join(__dirname, "Times", "/times new roman italic.ttf"),
      bolditalics: path.join(
        __dirname,
        "Times",
        "/fonts/times new roman italic.ttf"
      ),
    },

 

    san: {
      normal: path.join(__dirname, "san", "/DejaVuSansCondensed.ttf"),
      bold: path.join(__dirname, "san", "/DejaVuSansCondensed-Bold.ttf"),
      italics: path.join(__dirname, "san", "/DejaVuSansCondensed.ttf"),
      bolditalics: path.join(
        __dirname,
        "san",
        "/DejaVuSansCondensed.ttf"
      ),
    },

  };

  

  const printer = new pdfMake(fonts);
  const pdf = printer.createPdfKitDocument(dd);
  pdf.on("data", buffers.push.bind(buffers));


 let p = new Promise((resolve, reject) => {
    pdf.on("end", () => {
      let pdfData = Buffer.concat(buffers);
      fs.writeFileSync(_path, pdfData);
      return resolve({
          pdf_file_path: _path
      });
    });
  });

  console.log("done-===================")
  pdf.end();
  return p;
}

module.exports = generatePdfFromHtml;