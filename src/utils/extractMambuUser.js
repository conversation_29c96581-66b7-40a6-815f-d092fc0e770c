function extractMambuUser(signed_request) {
  // split the base64 grab first item
  let _signed = signed_request;
  _signed = signed_request.split(".")[1];

  // decode
  const data = new Buffer.from(_signed, "base64").toString("utf8");
  // parse json
  /**
   * {DOMAIN: 'premierkenya.sandbox.mambu.com', ALGORITHM: 'hmacSHA256', TENANT_ID: 'premierkenya', USER_KEY: '8a9387a815017f02d7ba9e0026'}
   */
  const decode_obj = JSON.parse(data);
  const userId = decode_obj["USER_KEY"];
  const domain = decode_obj["DOMAIN"];
  const sub = decode_obj["TENANT_ID"];
  const loanID= decode_obj?.OBJECT_ID;

  const mambu_env = domain.includes("sandbox") ? "sandbox" : "production";

  const base_url = "https://" + domain + "/api";

  return { userId, base_url, mambu_env, sub, loanID };
}

module.exports = extractMambuUser;
