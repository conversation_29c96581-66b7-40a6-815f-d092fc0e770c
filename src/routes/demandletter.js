const express = require("express");
const router = express.Router();
const Auth = require("../_middlewares/auth");

// ----------------------------

const pclDemandletterController = require("../controllers/pcldemandletter");

module.exports=(app)=>{
    // router.post("/proclamationDemandNotice",  pclDemandletterController.pclDemandletter(app));
    router.post("/proclamationDemandNotice",Auth,  pclDemandletterController.pclDemandletter(app));

    router.post("/proclamationNotice-webhook", function(req, res,next ){
        req.loanID = req.body.loanId;
        req.mambuData = {
          loanID: req.loanID,
          source:"ui"
        };
        return next();
    },   pclDemandletterController.pclDemandletter(app));

    router.get("/getReports",  pclDemandletterController.getReports(app));
   
    return router;
}
