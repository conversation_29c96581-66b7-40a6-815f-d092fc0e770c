require("dotenv").config();
const sgMail = require("@sendgrid/mail");

const apikey =
  "*********************************************************************";

sgMail.setApiKey(apikey);

module.exports = sendEmail;

async function sendEmail({
  to,
  subject,
  from = "<EMAIL>",
  ...rest
}) {
  try {
    const msg = {
      to,
      subject,
      from,
      ...rest,
    };

    console.log({
      apikey,
      p: process.env.sendgrid_key,
    });

    await sgMail.send(msg);
  } catch (error) {
    console.log({ error }, "kwa mail");
    throw error;
  }
}
