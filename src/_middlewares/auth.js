const extractMambuUser = require("../utils/extractMambuUser");

const auth = (req, res, next) => {
  
  if (req.body?.source == "mambu") {
    //  this is a webhook call

    req.loanID = req.body.loanID;
    req.mambuData = {
      ...req.body,
    };

    return next();
  }

  let idToken = req.body.signed_request || req.header("mambuUser");

  if (!idToken || idToken == "null") {
    console.error("no token found");
    return res.status(403).json({ message: "No token found,Unauthorized" });
  }

  console.log({
    idToken,
  });

  const { userId, base_url, mambu_env, sub, loanID } =
    extractMambuUser(idToken);

  req.userId = userId;
  req.loanID = loanID;

  req.mambuData = {
    userId,
    base_url,
    mambu_env,
    sub,
    loanID,
    source:"ui"
  };
  return next();
};

module.exports = auth;
