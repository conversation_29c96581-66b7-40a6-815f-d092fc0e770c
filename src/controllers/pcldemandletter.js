const ClientService = require("../services/clientService");

const { insertData } = require("../services/db");
const { selectData } = require("../services/db");
const { } = require('sequelize');

const MailService = require("../services/mailService");
const fs = require('fs/promises')
const dd = require("../utils/dd");
const pdfMake = require("../utils/pdf");
const getConfig = require("../utils/config");

const controller = {};

/**
 * this method will be used by webhook and ui
 * for ui it will send the signed request which will result in extraction of loanID from the base64, the rest of the data will be passed by the auth middleware
 *
 * for the webhook, we will require this payload below so as to by pass the auth middleware
 * @param {object} body - req.body
 * @param {string} loanID
 * @param {string} mambu_env - sandbox
 * @param {string} sub - platinumkenya
 * @param {string} source - mambu
 *
 */

controller.pclDemandletter = (app) => async function (req, res, next) {
  const loanID = req.loanID || req.body.loanID;

  let kind = req.body?.kind ?? "final";
  console.log("loanID", loanID);
  const ReportModel = app.get('sequelize').models.report;

  const { mambu_env = "production", sub = "platinumkenya" } = req?.mambuData || {
    // base_url: "https://platinumkenya.sandbox.mambu.com",
    mambu_env: "production",
    sub: "platinumkenya",
    source: "ui",
  };

  const config = getConfig({
    mambu_env,
    sub,
  });

  console.log({
    config,
    mambu_env,
    sub
  })

  if (!loanID) {
    return res.status(400).json({ message: "LoanID must be provided " });
  }
  let pathToAttachment = null;

  try {

    const loanAccount = await new ClientService(config).getLoanById(loanID);
    console.log("loanAccount", loanAccount)

    const date = new Date();
    const formattedDate = date.toISOString().split('T')[0];
    console.log(formattedDate);

    const { _ads001, encodedKey, id, activationDate, firstName, middleName, lastName, emailAddress = null, } = await new ClientService(
      config
    ).getClientById(loanAccount.accountHolderKey);


    let totalDue =
      loanAccount.balances.principalDue +
      loanAccount.balances.interestDue +
      loanAccount.balances.feesDue +
      loanAccount.balances.penaltyDue;

    // console.log("totalDue", totalDue);

    if (loanAccount.accountState == "ACTIVE_IN_ARREARS" &&  loanAccount.daysInArrears =="11") {

      const address = emailAddress;

      const activation_Date = new Date(activationDate)
      const formattedActivationDate = activation_Date.toISOString().split('T')[0];
      console.log("activationDate", activation_Date);

      // const fullName = firstName + "" + middleName + " " + "" + lastName
      const fullName = `${firstName}${middleName ? " " + middleName : ""
        } ${lastName}`.trim();

      const payload = {
        loan_account: loanAccount.id,
        Total_Due: totalDue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","),
        product_name: loanAccount.loanName,
        current_year: "",
        full_name: fullName,
        Email_Address: address,
        Postal_Address: _ads001 ? _ads001["POSTAL001"] : "",
        client_id: id,
        activation_date: formattedActivationDate,
        formattedDate: formattedDate

      }

      const daysInArrears = loanAccount.daysInArrears;
      console.log('daysInArrears ', daysInArrears);

      if (daysInArrears === 11) {
        kind = "final"; // Use "final" demand letter when daysInArrears is 11
      }

      // generate html from dd
      let html = null;
      let file_name = "_" + fullName + ".pdf";

      switch (kind) {
        case "final": {
          html = dd(payload);
          file_name = "ProclamationNotice" + file_name;
          title = "Proclamation Final Notice";
          break;
        }
      }

      const { pdf_file_path } = await pdfMake(html, file_name, kind)
      console.log({
        pdf_file_path
      })

      pathToAttachment = pdf_file_path;
      const attachPdf = await new ClientService(config).attachPdf({
        userId: loanID,
        fileName: file_name,
        title: title,
        file_path: pdf_file_path
      });


      // send email
      const sendEmail = await MailService._sendReportEmail(
        pdf_file_path,
        emailAddress
      );


      let errorMessage = "";
      let statusResult = "attached";
      let email_attached = true;


      if (!sendEmail.emailSent) {
        statusResult = "failed";
        email_attached = false;
      }

      if (!attachPdf.hasValue || !sendEmail.emailSent) {
        errorMessage = !sendEmail.emailSent
          ? sendEmail.emailData.errorReason
          : (!attachPdf.hasValue ? attachPdf.data.errorReason : "");
      }

      const report = await insertData(ReportModel, [{
        accountId: loanID,
        status: statusResult,
        email: emailAddress ?? "",
        errorReason: errorMessage,
        environment: mambu_env,
        sub,
        email_attached
      }]);

      return res.json({
        message: attachPdf?.data?.errorReason ? attachPdf.data.errorReason : "Pdf file attached",
        response: attachPdf,
      });

    } else {
      return res.json({
        
        daysInArrears: loanAccount.daysInArrears,
        message: "daysInArrears is not equal to 11 "
      });
    }
  } catch (error) {
    next(error);
  } finally {
    if (pathToAttachment) {
      await fs.unlink(pathToAttachment);
    }
  }
};

controller.getReports = function (app) {
  return async function (req, res) {
    try {
      const { report } = app.get('sequelize').models;
      return selectData(report, {}).then(data => {
        return res.send(data);
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ message: error.message });
    }
  };
}

module.exports = controller;



