const express = require('express');
const morgan = require('morgan');
const helmet = require('helmet');
const cors = require('cors');
const session = require('./utils/session');
require('dotenv').config();
const path = require("path");

const sequelize = require('./sequilize.js');
const models = require('./models/report.model.js')

const api = require("./routes/demandletter.js");

const { selectData } = require('./services/db.js');

const middlewares = require('./middleware');

const fronteend = path.join(__dirname, "../public/dist");

// console.log({fronteend})

const app = express(); 

app.use(morgan("dev"));
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(fronteend));
app.use(express.static("public"));


app.get("/demandnotice/app-definition/", async (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../public/dev.xml"));
});

app.post("/demandnotice/ui", (req, res, next) => {
  console.log(req.body);
  const _signed = req.body.signed_request;
  session(_signed);
  req.method = "GET";
  next();
});

sequelize(app);
models(app)

app.use(
  "/demandnotice/ui",
  express.static(path.join(__dirname, "../public/dist"))
);
app.use('/demandnotice/v1', api(app));



app.use(middlewares.notFound);
app.use(middlewares.errorHandler);

module.exports = app;


