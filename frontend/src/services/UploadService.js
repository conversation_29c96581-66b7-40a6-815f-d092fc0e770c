import axios from "axios";

class UploadService {
  constructor() {}
  static async upload(file, route = "/cars/") {
    let formData = new FormData();
    formData.append("excelfile", file);

    const res = await axios.post(route, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      // nice to have upload progress handler
    });

    return res.data;
  }

  static async getReports() {
    const reports = await axios.get("/getReports");
    return reports.data;
  }

}

export default UploadService;
