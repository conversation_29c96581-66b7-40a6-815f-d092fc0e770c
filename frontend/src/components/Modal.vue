<template>
  <v-row justify="space-around">
    <v-col cols="auto">
      <v-dialog transition="dialog-bottom-transition" max-width="700" v-model="dialog">
        <template>
          <v-card>
            <v-toolbar color="#C2185B" dark>Alert</v-toolbar>
            <v-card-text>
              <div class="text-h4 pa-12">{{message}}</div>
            </v-card-text>
            <v-card-actions class="justify-end">
              <v-btn text @click="closeModal">Close</v-btn>
            </v-card-actions>
          </v-card>
        </template>
      </v-dialog>
    </v-col>
  </v-row>
</template>

<script>
export default {
  computed: {
    dialog() {
      return this.$store.state.modalState.value;
    },

    message(){
        return this.$store.state.modalState.message;
    }
  },

  methods: {
    closeModal() {
      this.$store.dispatch("toggleModal", "");
    },
  },
};
</script>

