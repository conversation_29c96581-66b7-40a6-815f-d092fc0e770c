import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import vuetify from "./plugins/vuetify";
import axios from "axios";
import config from "./config";
import store from "./store/proclamation.service";
import excel from 'vue-excel-export';

// let mambuUser =
//     localStorage.getItem("mambuUser") || null;
// axios.defaults.baseURL = config["BACKEND_SERVICE"];
// axios.defaults.headers["mambuUser"] = mambuUser;

let mambuUser =
  localStorage.getItem("mambuUser") ||
  "9b1baabc5e753ce690f459ec9607c8fb9e2048bb42dfc910a6fee76e994f40e4.eyJET01BSU4iOiJwbGF0aW51bWtlbnlhLnNhbmRib3gubWFtYnUuY29tIiwiT0JKRUNUX0lEIjoiMTAwMDYzNDMyMCIsIkFMR09SSVRITSI6ImhtYWNTSEEyNTYiLCJURU5BTlRfSUQiOiJwbGF0aW51bWtlbnlhIiwiVVNFUl9LRVkiOiI4YTkzODY1OTg4YmI1ODg4MDE4OGJlMTM1NTY3MDE3ZCJ9";

axios.defaults.baseURL = config["BACKEND_SERVICE"];
axios.defaults.headers["mambuUser"] = mambuUser;

// Add a request interceptor
Vue.config.productionTip = false;

axios.interceptors.request.use(
  function (config) {
    // Do something before request is sent
    const isSearching = store.state.isSearching;

    if (isSearching) return config;
    store.dispatch("isLoading", true);
    return config;
  },

  function (error) {
    store.dispatch("isLoading", false).then(() => {
      store.dispatch(
        "toggleModal",
        error.response.data.message || error.message || error
      );
    });
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
axios.interceptors.response.use(
  function (response) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    store.dispatch("isLoading", false);
    return response;
  },

  function (error) {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    console.log(JSON.stringify(error, null, 3), "-====");
    // redirect to 401.vue
    if (error.response.status == "401" || error.response.status == "403") {
      router.replace({
        name: "401",
      });
    }

    // store.dispatch("isLoading", false).then(() => {
    //   store.dispatch("toggleModal", error.response.data.message || error.message || error);
    // });
    store.dispatch("isLoading", false).then(() => {
      store.dispatch("toggleModal", error.message || error);
    });
    return Promise.reject(error);
  }
);

Vue.use(excel);
new Vue({
  router,
  vuetify,
  store,
  render: (h) => h(App),
}).$mount("#app");
