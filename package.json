{"name": "express-api-starter", "version": "1.2.0", "description": "A basic starter for an express.js API", "main": "index.js", "scripts": {"start": "node src/index.js", "server": "nodemon src/index.js", "lint": "eslint --fix src", "test": "jest", "client": "cd frontend && yarn run dev", "dev": "concurrently \"npm run server\" \"npm run client\""}, "keywords": [], "author": "<PERSON><PERSON> <PERSON>. <<EMAIL>> (https://w3cj.now.sh)", "repository": {"type": "git", "url": "https://github.com/w3cj/express-api-starter.git"}, "license": "MIT", "dependencies": {"@sendgrid/mail": "^7.7.0", "app-root-path": "^3.1.0", "axios": "^0.27.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.0.1", "express": "^4.18.2", "form-data": "^4.0.0", "handlebars": "^4.7.7", "handlers": "^1.0.3", "helmet": "^5.1.1", "html-to-json": "^0.6.0", "html-to-pdfmake": "^2.4.16", "jsdom": "^21.1.0", "morgan": "^1.10.0", "node-express-api-starter": "^1.0.0", "pdfmake": "^0.2.7", "pg": "^8.11.1", "sequelize": "^6.32.1", "vue-excel-export": "^0.1.3", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"concurrently": "^7.6.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "jest": "^28.1.3", "nodemon": "^2.0.19", "supertest": "^6.2.4"}, "imports": {"#src/*": "./src/*.js", "#api/*": "./src/api/*.js", "#controllers/*": "./src/controllers/*.js", "#routes/*": "./src/routes/*.js", "#services/*": "./src/services/*.js", "#utils/*": "./src/utils/*.js", "#jobs/*": "./src/jobs/*.js", "#db/*": "./src/db/*.js", "#models/*": "./src/db/models/*.js", "#middlewares/*": "./src/_middlewares/*.js"}}